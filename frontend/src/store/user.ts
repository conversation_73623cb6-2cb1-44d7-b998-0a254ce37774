import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getToken, setToken, removeToken } from '@/utils'
import { login, getUserProfile, getStorageInfo } from '@/api/user'
import type { User, LoginForm, StorageInfo } from '@/types'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(getToken() || '')
  const userInfo = ref<User | null>(null)
  const storageInfo = ref<StorageInfo | null>(null)
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const avatar = computed(() => userInfo.value?.avatar_url || '')
  const nickname = computed(() => userInfo.value?.nickname || userInfo.value?.username || '')
  const usagePercentage = computed(() => storageInfo.value?.usage_percentage || 0)
  
  // 登录
  const loginAction = async (loginForm: LoginForm) => {
    try {
      const response = await login(loginForm)
      token.value = response.token
      userInfo.value = response.user
      setToken(response.token)
      
      // 获取存储信息
      await getStorageInfoAction()
      
      ElMessage.success('登录成功')
      return response
    } catch (error) {
      throw error
    }
  }
  
  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const user = await getUserProfile()
      userInfo.value = user
      return user
    } catch (error) {
      // 如果获取用户信息失败，清除token
      logout()
      throw error
    }
  }
  
  // 获取存储信息
  const getStorageInfoAction = async () => {
    try {
      const info = await getStorageInfo()
      storageInfo.value = info
      return info
    } catch (error) {
      console.error('获取存储信息失败:', error)
    }
  }
  
  // 更新用户信息
  const updateUserInfo = (user: Partial<User>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...user }
    }
  }
  
  // 更新存储信息
  const updateStorageInfo = (info: Partial<StorageInfo>) => {
    if (storageInfo.value) {
      storageInfo.value = { ...storageInfo.value, ...info }
    }
  }
  
  // 登出
  const logout = () => {
    token.value = ''
    userInfo.value = null
    storageInfo.value = null
    removeToken()
  }
  
  // 初始化（应用启动时调用）
  const init = async () => {
    if (token.value) {
      try {
        console.log('开始初始化用户信息...')

        // 设置超时，避免长时间卡住
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('初始化超时')), 10000) // 10秒超时
        })

        // 并行获取用户信息和存储信息，但有超时保护
        await Promise.race([
          Promise.all([
            getUserInfoAction(),
            getStorageInfoAction()
          ]),
          timeoutPromise
        ])

        console.log('用户信息初始化成功')
      } catch (error) {
        console.error('初始化用户信息失败:', error)
        // 初始化失败时清除可能无效的token
        if (error.message === '初始化超时' || error.response?.status === 401) {
          console.log('清除无效token')
          logout()
        }
      }
    } else {
      console.log('没有token，跳过用户信息初始化')
    }
  }
  
  return {
    // 状态
    token,
    userInfo,
    storageInfo,
    
    // 计算属性
    isLoggedIn,
    avatar,
    nickname,
    usagePercentage,
    
    // 方法
    loginAction,
    getUserInfoAction,
    getStorageInfoAction,
    updateUserInfo,
    updateStorageInfo,
    logout,
    init,
  }
})
