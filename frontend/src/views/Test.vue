<template>
  <div class="test-page">
    <h1>前后端连接测试</h1>
    
    <div class="test-section">
      <h2>1. 前端状态</h2>
      <p>✅ 前端 Vue 应用正常运行</p>
      <p>当前时间: {{ currentTime }}</p>
    </div>
    
    <div class="test-section">
      <h2>2. 后端连接测试</h2>
      <el-button @click="testBackend" :loading="testing">测试后端连接</el-button>
      <div v-if="backendResult" class="result">
        <h3>后端响应:</h3>
        <pre>{{ JSON.stringify(backendResult, null, 2) }}</pre>
      </div>
      <div v-if="backendError" class="error">
        <h3>连接错误:</h3>
        <pre>{{ backendError }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>3. 用户注册测试</h2>
      <el-form :model="testUser" style="max-width: 400px">
        <el-form-item label="用户名">
          <el-input v-model="testUser.username" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="testUser.email" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="testUser.password" type="password" />
        </el-form-item>
        <el-form-item>
          <el-button @click="testRegister" :loading="registering">测试注册</el-button>
        </el-form-item>
      </el-form>
      <div v-if="registerResult" class="result">
        <h3>注册结果:</h3>
        <pre>{{ JSON.stringify(registerResult, null, 2) }}</pre>
      </div>
      <div v-if="registerError" class="error">
        <h3>注册错误:</h3>
        <pre>{{ registerError }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'

const currentTime = ref('')
const testing = ref(false)
const backendResult = ref(null)
const backendError = ref('')

const registering = ref(false)
const registerResult = ref(null)
const registerError = ref('')

const testUser = ref({
  username: 'testuser',
  email: '<EMAIL>',
  password: '123456'
})

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 测试后端连接
const testBackend = async () => {
  testing.value = true
  backendResult.value = null
  backendError.value = ''
  
  try {
    console.log('正在测试后端连接...')
    
    // 测试健康检查接口
    const response = await axios.get('/api/health')
    console.log('后端响应:', response.data)
    
    backendResult.value = response.data
  } catch (error: any) {
    console.error('后端连接失败:', error)
    backendError.value = error.message || '连接失败'
    
    if (error.response) {
      backendError.value += `\n状态码: ${error.response.status}`
      backendError.value += `\n响应: ${JSON.stringify(error.response.data, null, 2)}`
    }
  } finally {
    testing.value = false
  }
}

// 测试用户注册
const testRegister = async () => {
  registering.value = true
  registerResult.value = null
  registerError.value = ''
  
  try {
    console.log('正在测试用户注册...')
    
    const response = await axios.post('/api/auth/register', {
      username: testUser.value.username,
      email: testUser.value.email,
      password: testUser.value.password,
      confirmPassword: testUser.value.password
    })
    
    console.log('注册响应:', response.data)
    registerResult.value = response.data
  } catch (error: any) {
    console.error('注册失败:', error)
    registerError.value = error.message || '注册失败'
    
    if (error.response) {
      registerError.value += `\n状态码: ${error.response.status}`
      registerError.value += `\n响应: ${JSON.stringify(error.response.data, null, 2)}`
    }
  } finally {
    registering.value = false
  }
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
  
  // 自动测试后端连接
  setTimeout(testBackend, 1000)
})
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.result {
  margin-top: 15px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #b3e5fc;
  border-radius: 4px;
}

.error {
  margin-top: 15px;
  padding: 15px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
