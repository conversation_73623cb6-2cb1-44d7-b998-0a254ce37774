@echo off
echo.
echo ========================================
echo JiangJiang NetDisk Diagnostic Tool
echo ========================================
echo.

echo Checking service status...
echo.

echo Port usage:
echo Backend port 8080:
netstat -an | findstr :8080
echo.
echo Frontend port 3000:
netstat -an | findstr :3000
echo.

echo Testing backend API:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/health' -Method GET -TimeoutSec 5; Write-Host 'Backend API OK' -ForegroundColor Green; Write-Host $response.Content } catch { Write-Host 'Backend API Failed:' $_.Exception.Message -ForegroundColor Red }"
echo.

echo Testing frontend service:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000' -Method GET -TimeoutSec 5; Write-Host 'Frontend service OK' -ForegroundColor Green } catch { Write-Host 'Frontend service Failed:' $_.Exception.Message -ForegroundColor Red }"
echo.

echo Testing frontend API proxy:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/api/health' -Method GET -TimeoutSec 5; Write-Host 'Frontend API proxy OK' -ForegroundColor Green; Write-Host $response.Content } catch { Write-Host 'Frontend API proxy Failed:' $_.Exception.Message -ForegroundColor Red }"
echo.

echo Process information:
echo Node.js processes:
tasklist | findstr node.exe
echo.
echo Go processes:
tasklist | findstr go.exe
echo.

echo Access URLs:
echo Frontend: http://localhost:3000
echo Test page: http://localhost:3000/test
echo Backend API: http://localhost:8080/api/health
echo.

echo If frontend shows loading bar forever:
echo 1. Open browser developer tools (F12)
echo 2. Check Console tab for errors
echo 3. Check Network tab for requests
echo 4. Try test page: http://localhost:3000/test
echo.

pause
