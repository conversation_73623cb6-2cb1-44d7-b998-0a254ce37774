package main

//
//import (
//	"fmt"
//	"gorm.io/driver/postgres"
//	"gorm.io/gorm"
//)
//
//func main() {
//	// 1. 连接字符串配置
//	dsn := "host=localhost user=postgres password=123456 dbname=jiangjiang_netdisk port=5432 sslmode=disable"
//
//	// 2. 连接数据库
//	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
//	if err != nil {
//		panic("连接失败: " + err.Error())
//	}
//
//	// 3. 验证连接
//	sqlDB, _ := db.DB()
//	fmt.Println("数据库连接成功:", sqlDB.Stats())
//
//	// 4. 定义模型
//	//type User struct {
//	//	gorm.Model
//	//	Name string
//	//}
//	//
//	//// 5. 自动迁移表结构
//	//db.AutoMigrate(&User{})
//	//
//	//fmt.Println("GORM 连接 PostgreSQL 成功!")
//}
